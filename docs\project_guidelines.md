---
inclusion: always
---

# vLLM推理服务压测工具 - 项目开发指导原则

## 代码结构原则

### 模块化设计
- 每个模块职责单一，功能明确
- 模块间通过明确的接口交互
- 避免循环依赖和紧耦合

### 目录结构规范
```
src/
├── core/          # 核心压测引擎
├── datasets/      # 数据集处理
├── backends/      # 后端适配
├── utils/         # 工具函数
└── aggregation/   # 结果处理
```

## 编码规范

### Python代码风格
- 遵循PEP 8编码规范
- 使用类型提示 (Type Hints)
- 函数和类都要有详细的文档字符串
- 变量和函数命名要清晰表达意图

### 导入规范
- 使用相对导入 (`from ..module import`)
- 按标准库、第三方库、本地模块的顺序组织导入
- 每个模块都要有 `__init__.py` 文件

### 异步编程规范
- 使用 `asyncio` 进行异步编程
- 正确处理异步上下文和资源清理
- 使用 `aiohttp` 进行HTTP请求

## 性能指标定义

### 核心指标
- **TTFT**: Time to First Token (首token时间)
- **TPOT**: Time per Output Token (每token输出时间)
- **ITL**: Inter-token Latency (token间延迟)
- **E2EL**: End-to-End Latency (端到端延迟)
- **Throughput**: 吞吐量 (请求/秒, token/秒)

### 统计方法
- 计算平均值、中位数、标准差
- 提供P99、P95等百分位数
- 支持多种聚合方式

## 配置管理

### 配置文件格式
- 使用YAML格式的配置文件
- 支持参数组合的笛卡尔积测试
- 提供详细的配置说明和示例

### 环境变量支持
- 支持通过环境变量覆盖配置
- 敏感信息通过环境变量传递
- 提供默认值和验证

## 错误处理

### 异常处理原则
- 使用具体的异常类型
- 提供有意义的错误信息
- 记录详细的错误日志
- 优雅地处理网络和服务异常

### 重试机制
- 对网络请求实现重试机制
- 使用指数退避策略
- 设置合理的超时时间

## 测试和验证

### 单元测试
- 为核心功能编写单元测试
- 使用mock对象模拟外部依赖
- 保持测试的独立性和可重复性

### 集成测试
- 测试与vLLM服务器的集成
- 验证不同配置下的行为
- 测试异常情况的处理

## 文档要求

### 代码文档
- 每个模块都要有清晰的文档字符串
- 函数参数和返回值要有类型注解
- 复杂逻辑要有内联注释

### 用户文档
- 提供详细的使用说明
- 包含完整的示例代码
- 说明配置选项和参数含义

## 兼容性要求

### 向后兼容
- 保持现有API的兼容性
- 新功能通过可选参数添加
- 废弃功能要有迁移指南

### 版本管理
- 使用语义化版本号
- 记录版本变更日志
- 标记破坏性变更

## 性能优化

### 异步优化
- 最大化并发处理能力
- 避免阻塞操作
- 合理使用连接池

### 内存管理
- 及时释放不需要的资源
- 避免内存泄漏
- 监控内存使用情况

## 安全考虑

### 输入验证
- 验证所有用户输入
- 防止注入攻击
- 限制资源使用

### 敏感信息
- 不在日志中记录敏感信息
- 使用安全的配置管理
- 遵循最小权限原则