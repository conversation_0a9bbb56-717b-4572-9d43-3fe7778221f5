#!/usr/bin/env python3
"""
API 配置测试脚本

功能：
- 测试 .env 文件是否正确加载
- 验证 API Key 是否正确配置
- 提供配置诊断信息
"""

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.config import load_env_file, is_api_key_configured, get_api_key, get_config_value


def test_env_loading():
    """测试环境变量加载"""
    print("=== 环境变量加载测试 ===")
    
    # 重新加载环境变量
    load_env_file()
    
    # 检查 API Key
    if is_api_key_configured():
        api_key = get_api_key()
        masked_key = f"{api_key[:4]}{'*' * (len(api_key) - 8)}{api_key[-4:]}" if len(api_key) > 8 else "****"
        print(f"✓ OPENAI_API_KEY: {masked_key}")
        print(f"✓ API Key 长度: {len(api_key)} 字符")
    else:
        print("❌ OPENAI_API_KEY: 未配置")
    
    # 检查其他可能的环境变量
    other_vars = ['VLLM_USE_MODELSCOPE', 'HF_HUB_OFFLINE']
    for var in other_vars:
        value = get_config_value(var)
        if value:
            print(f"✓ {var}: {value}")
        else:
            print(f"- {var}: 未设置")


def test_env_file_exists():
    """测试 .env 文件是否存在"""
    print("\n=== .env 文件检查 ===")
    
    env_file = ".env"
    if os.path.exists(env_file):
        print(f"✓ .env 文件存在: {os.path.abspath(env_file)}")
        
        # 读取文件内容（隐藏敏感信息）
        try:
            with open(env_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            print(f"✓ 文件行数: {len(lines)}")
            print("文件内容预览（敏感信息已隐藏）:")
            
            for i, line in enumerate(lines, 1):
                line = line.strip()
                if not line or line.startswith('#'):
                    print(f"  {i:2d}: {line}")
                elif '=' in line:
                    key, value = line.split('=', 1)
                    if 'KEY' in key.upper() or 'TOKEN' in key.upper() or 'SECRET' in key.upper():
                        masked_value = f"{value[:4]}{'*' * max(0, len(value) - 8)}{value[-4:]}" if len(value) > 8 else "****"
                        print(f"  {i:2d}: {key}={masked_value}")
                    else:
                        print(f"  {i:2d}: {line}")
                else:
                    print(f"  {i:2d}: {line}")
                    
        except Exception as e:
            print(f"❌ 读取 .env 文件失败: {e}")
    else:
        print(f"❌ .env 文件不存在: {os.path.abspath(env_file)}")


def test_api_key_format():
    """测试 API Key 格式"""
    print("\n=== API Key 格式检查 ===")
    
    api_key = get_api_key()
    if not api_key:
        print("❌ API Key 未配置")
        return
    
    # 基本格式检查
    if len(api_key) < 10:
        print("⚠️  API Key 长度可能过短")
    else:
        print("✓ API Key 长度合适")
    
    # 检查是否包含特殊字符
    if api_key.strip() != api_key:
        print("⚠️  API Key 包含前导或尾随空格")
    else:
        print("✓ API Key 格式正确")
    
    # 检查是否为示例值
    if api_key.startswith("sk-") or api_key == "your-api-key-here":
        print("⚠️  API Key 可能是示例值，请确认是否为真实密钥")
    else:
        print("✓ API Key 不是常见的示例值")


def main():
    """主函数"""
    print("🔧 API 配置诊断工具")
    print("=" * 50)
    
    test_env_file_exists()
    test_env_loading()
    test_api_key_format()
    
    print("\n" + "=" * 50)
    if is_api_key_configured():
        print("🎉 配置检查完成！API Key 已正确配置。")
    else:
        print("❌ 配置检查完成！请检查 API Key 配置。")
        print("\n解决方案:")
        print("1. 确保项目根目录存在 .env 文件")
        print("2. 在 .env 文件中添加: OPENAI_API_KEY=your-actual-api-key")
        print("3. 重新运行此脚本进行验证")


if __name__ == "__main__":
    main()
