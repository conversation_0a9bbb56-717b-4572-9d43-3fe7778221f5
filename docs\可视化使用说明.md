# vLLM压测结果可视化使用说明

## 概述

本工具提供了两种可视化方案来展示vLLM压测结果中的关键性能指标：

1. **简单可视化** (`visualize_results_simple.py`) - 无外部依赖，生成文本报告
2. **高级可视化** (`visualize_results.py`) - 需要matplotlib等依赖，生成专业图表

## 关键性能指标

| 指标名称 | 英文缩写 | 中文说明 | 英文说明 | 单位 | 备注 |
|----------|----------|----------|----------|------|------|
| 输入令牌数 | prompt_tokens | 输入令牌数量 | Input Token Count | tokens | 总输入token数 |
| 输出令牌数 | completion_tokens | 输出令牌数量 | Output Token Count | tokens | 总输出token数 |
| 总吞吐量 | TOTAL_THROUGHPUT | 总令牌吞吐量 | Total Token Throughput | tokens/s | 输入+输出token处理速度 |
| 生成吞吐量 | generate_throughput | 生成令牌吞吐量 | Generation Throughput | tokens/s | 仅输出token生成速度 |
| 首令牌时间 | TTFT | 首令牌时间 | Time to First Token | ms | 从请求到首个token的延迟 |
| 每令牌时间 | TPOT | 每令牌输出时间 | Time per Output Token | ms | 平均每个输出token的时间 |
| 令牌间延迟 | ITL | 令牌间延迟 | Inter-token Latency | ms | 相邻token间的生成间隔 |

## 使用方法

### 1. 简单可视化（推荐）

#### 基本用法
```bash
# 自动查找最新的聚合结果CSV文件
python3 visualize_results_simple.py

# 指定特定的CSV文件
python3 visualize_results_simple.py --csv results/DeepSeek-R1_20250728_152452/aggregate_results_20250728.csv

# 指定输出目录
python3 visualize_results_simple.py --output my_reports
```

#### 生成的报告文件

1. **performance_report.txt** - 详细性能报告
   - 最佳性能配置汇总
   - 详细性能数据表格
   - 令牌统计信息

2. **performance_summary.csv** - 性能摘要表格
   - 中英文双语列名
   - 关键指标汇总
   - 便于Excel打开分析

3. **ascii_charts.txt** - ASCII字符图表
   - 总令牌吞吐量对比
   - TTFT对比（越短越好）
   - TPOT对比（越短越好）

4. **performance_ranking.txt** - 性能排名
   - 按总吞吐量排名
   - 按TTFT排名（越小越好）
   - 按TPOT排名（越小越好）
   - 综合评分排名

### 2. 高级可视化（需要依赖包）

#### 安装依赖
```bash
pip install matplotlib seaborn scikit-learn pandas
```

#### 基本用法
```bash
# 自动查找最新的聚合结果CSV文件
python3 visualize_results.py

# 指定特定的CSV文件
python3 visualize_results.py --csv results/aggregate_results_20250728.csv --output charts
```

#### 生成的图表文件

1. **throughput_comparison.png** - 吞吐量对比图表
   - 总令牌吞吐量对比
   - 生成吞吐量对比
   - 令牌数量对比
   - 请求吞吐量对比

2. **latency_comparison.png** - 延迟性能对比图表
   - TTFT对比
   - TPOT对比
   - ITL对比
   - 端到端延迟对比

3. **performance_heatmap.png** - 性能热力图
   - 多指标综合展示
   - 颜色编码性能好坏
   - 便于快速识别最佳配置

4. **comprehensive_dashboard.png** - 综合仪表板
   - 所有关键指标的综合展示
   - 包含性能摘要表格
   - 一页式完整报告

5. **performance_report.txt** - 文本性能报告

## 自动化集成

### 聚合时自动生成报告

当运行 `python3 main.py aggregate` 时，系统会自动调用简单可视化工具生成报告：

```bash
python3 main.py aggregate
# 输出示例：
# 已聚合 4 个测试结果 → results/DeepSeek-R1_20250728_152452/aggregate_results_20250728.csv
# 
# 正在生成可视化报告...
# ✓ 可视化报告生成成功
# 报告文件位置: reports/
```

### 批量测试完整流程

```bash
# 1. 执行批量压测
python3 main.py batch

# 2. 聚合结果并自动生成可视化报告
python3 main.py aggregate

# 3. 查看生成的报告
ls reports/
```

## 性能分析指导

### 吞吐量分析
- **总吞吐量**：越高越好，表示系统整体处理能力
- **生成吞吐量**：越高越好，表示文本生成速度
- **请求吞吐量**：越高越好，表示并发处理能力

### 延迟分析
- **TTFT**：越低越好，影响用户感知的响应速度
- **TPOT**：越低越好，影响文本生成的流畅度
- **ITL**：越低越好，影响token流式输出的连续性

### 配置优化建议

根据生成的排名报告：

1. **高吞吐量场景**：选择总吞吐量排名靠前的配置
2. **低延迟场景**：选择TTFT和TPOT排名靠前的配置
3. **平衡场景**：参考综合评分排名

### 典型性能模式

- **512x512_mc1**：低延迟，适合实时对话
- **512x512_mc4**：高吞吐量，适合批量处理
- **1024x1024_mc1**：长文本，低并发
- **1024x1024_mc4**：长文本，高并发

## 故障排除

### 常见问题

1. **找不到CSV文件**
   ```
   错误: 未找到聚合结果CSV文件
   解决: 先运行 python3 main.py aggregate 生成聚合结果
   ```

2. **数据加载失败**
   ```
   错误: 未能加载有效数据
   解决: 检查CSV文件格式，确保包含必要的列
   ```

3. **依赖包缺失**（仅高级可视化）
   ```
   错误: ModuleNotFoundError: No module named 'matplotlib'
   解决: pip install matplotlib seaborn scikit-learn pandas
   ```

### 手动验证数据

```bash
# 快速查看CSV文件内容
python3 -c "
import csv
with open('results/aggregate_results_20250728.csv', 'r') as f:
    reader = csv.DictReader(f)
    next(reader)  # 跳过中文列名
    for i, row in enumerate(reader):
        if i < 3:  # 只显示前3行
            print(f'配置: {row[\"input_len\"]}x{row[\"output_len\"]}_mc{row[\"max_concurrency\"]}')
            print(f'吞吐量: {row[\"total_token_throughput\"]} tokens/s')
            print('---')
"
```

## 扩展开发

### 添加新的指标

在 `visualize_results_simple.py` 中的 `load_csv_data` 函数里添加新的字段处理：

```python
row['new_metric'] = float(row['source_column'])
```

### 自定义报告格式

修改 `create_text_report` 函数来调整报告格式和内容。

### 集成到CI/CD

可以将可视化脚本集成到自动化测试流程中：

```bash
#!/bin/bash
# 自动化测试脚本
python3 main.py batch
python3 main.py aggregate
python3 visualize_results_simple.py

# 将报告上传到监控系统或发送邮件
```