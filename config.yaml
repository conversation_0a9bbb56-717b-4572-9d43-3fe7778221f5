# =========================
# vLLM推理服务压测配置文件
# =========================

# 指定要测试的模型路径
model: "/data/model/cognitivecomputations/DeepSeek-R1-awq"  # 模型本地路径

# vLLM服务的基础URL
base_url: "http://localhost:8010"  # vLLM推理服务地址

# 指定分词器路径（通常与模型路径一致）
tokenizer: "/data/model/cognitivecomputations/DeepSeek-R1-awq"  # 分词器本地路径

# =========================
# 输入输出长度组合设置
# =========================
# 用于测试不同输入长度和输出长度的组合
# 每个元素为 [输入token数, 输出token数]
input_output:
  - [512, 512]    # 中等长度对话（输入512，输出512）
  - [1024, 1024]  # 长文本处理（输入1024，输出1024）

# =========================
# 并发与请求数设置
# =========================
# concurrency_prompts 列表，每个元素为 [并发数, 请求总数]
# 例如 [1, 10] 表示串行（1并发），共10个请求
#      [4, 40] 表示4并发，共40个请求
concurrency_prompts:
  - [1, 10]   # 串行基准测试（1并发，10请求）
  - [4, 40]   # 中等并发压力测试（4并发，40请求）