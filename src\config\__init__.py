# 配置管理模块
"""
配置管理模块

功能：
- 加载环境变量配置
- 提供统一的配置访问接口
- 支持从 .env 文件加载配置
"""

import os
from pathlib import Path
from typing import Optional


def load_env_file(env_path: Optional[str] = None) -> None:
    """
    加载 .env 文件中的环境变量

    参数:
        env_path (str, optional): .env 文件路径，默认为项目根目录下的 .env 文件
    """
    if env_path is None:
        # 获取项目根目录（假设此文件在 src/config/ 目录下）
        project_root = Path(__file__).parent.parent.parent
        env_path = project_root / ".env"

    if not os.path.exists(env_path):
        print(f"警告: 未找到 .env 文件: {env_path}")
        return

    try:
        with open(env_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                # 跳过空行和注释行
                if not line or line.startswith('#'):
                    continue

                # 解析 KEY=VALUE 格式
                if '=' in line:
                    key, value = line.split('=', 1)
                    key = key.strip()
                    value = value.strip()

                    # 移除值两端的引号（如果有）
                    if value.startswith('"') and value.endswith('"'):
                        value = value[1:-1]
                    elif value.startswith("'") and value.endswith("'"):
                        value = value[1:-1]

                    # 只有当环境变量不存在时才设置
                    if key not in os.environ:
                        os.environ[key] = value

        print(f"✓ 成功加载环境变量配置: {env_path}")
    except Exception as e:
        print(f"错误: 加载 .env 文件失败: {e}")


def get_api_key() -> Optional[str]:
    """
    获取 API Key

    返回:
        str: API Key，如果未设置则返回 None
    """
    return os.environ.get('OPENAI_API_KEY')


def get_config_value(key: str, default: Optional[str] = None) -> Optional[str]:
    """
    获取配置值

    参数:
        key (str): 配置键名
        default (str, optional): 默认值

    返回:
        str: 配置值
    """
    return os.environ.get(key, default)


def is_api_key_configured() -> bool:
    """
    检查 API Key 是否已配置

    返回:
        bool: 如果 API Key 已配置返回 True，否则返回 False
    """
    api_key = get_api_key()
    return api_key is not None and api_key.strip() != ""


# 自动加载 .env 文件
load_env_file()