# =============================================================================
# vLLM推理服务压测工具依赖包
# =============================================================================
# 安装命令: pip install -r requirements.txt

# -----------------------------------------------------------------------------
# 核心依赖包【Nvidia GPU 环境】
# -----------------------------------------------------------------------------
# transformers==4.45.0
# aiohttp==3.11.18
# pandas==2.1.3
# datasets==2.16.0
# matplotlib==3.7.0
# seaborn==0.11.0
# numpy==1.25.0
# plotly==5.0.0


# -----------------------------------------------------------------------------
# 核心依赖包【海光DCU K100-AI(DTK25.04)环境】
# -----------------------------------------------------------------------------
# vLLM推理引擎 - 用于分词器和工具函数
# 注意：这里使用的是海光DCU K100-AI(DTK25.04)优化版本
# vllm==0.6.2+das.opt3.dtk2504
https://download.sourcefind.cn:65024/file/4/vllm/DAS1.5/vllm-0.6.2+das.opt3.dtk2504-cp310-cp310-manylinux_2_28_x86_64.whl


# HuggingFace变换器库 - 提供分词器和模型工具
transformers==4.45.0

# 异步HTTP客户端 - 用于发送压测请求到vLLM服务器
aiohttp==3.11.18

# 数据处理和分析库 - 用于结果聚合、统计分析和CSV生成
pandas==2.1.3
numpy==1.25.0

# HuggingFace数据集库 - 用于加载各种测试数据集
datasets==2.14.1
# PyArrow - datasets的依赖，需要兼容版本避免PyExtensionType错误
# 注意：pyarrow 21.0.0+ 与 datasets 2.14.1 不兼容，会出现 PyExtensionType 错误
pyarrow==14.0.1

# 进度条显示 - 用于异步任务进度显示
tqdm==4.67.1

# 可视化库 - 用于结果图表生成（可选）
matplotlib==3.7.0
seaborn==0.11.0
plotly==5.0.0
scikit-learn==1.3.0  # 机器学习库（用于数据标准化）

# 其他工具依赖
pyyaml==6.0.2  # YAML配置文件解析
requests==2.32.3 # HTTP请求库（备用）
pillow==11.3.0  # 图像处理库（用于多模态数据集）
huggingface-hub==0.34.1  # HuggingFace Hub客户端